[project]
name = "audio-chat-backend"
version = "0.1.0"
description = "Backend for audio chat application with OpenAI integration"
requires-python = ">=3.8"
dependencies = [
    "fastapi==0.104.1",
    "uvicorn==0.24.0",
    "openai>=1.35.0",
    "python-multipart==0.0.6",
    "python-dotenv==1.0.0",
    "openwakeword==0.6.0",
    "websockets==12.0",
    "websocket-client>=1.6.0",
    "numpy==1.24.3",
    "aiohttp>=3.10.11",
    "pywebostv>=0.8.9",
    "wakeonlan>=3.1.0",
    "requests>=2.32.4",
]

[tool.uv]
dev-dependencies = []

[tool.hatch.build.targets.wheel]
packages = ["."]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
