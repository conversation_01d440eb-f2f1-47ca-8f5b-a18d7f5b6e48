"""
Web search tool implementation using Serper API
"""
import os
import json
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from dotenv import load_dotenv

load_dotenv()


class WebSearchResult(BaseModel):
    """Individual search result"""
    title: str
    link: str
    snippet: str
    position: Optional[int] = None


class WebSearchResponse(BaseModel):
    """Response from web search"""
    query: str
    results: List[WebSearchResult]
    answer_box: Optional[Dict[str, Any]] = None
    knowledge_graph: Optional[Dict[str, Any]] = None


class WebSearchTool(BaseModel):
    """Web search tool parameters following OpenAI function calling schema"""
    query: str = Field(description="The search query to look up on the web")
    max_results: int = Field(
        default=5, 
        description="Maximum number of results to return",
        ge=1,
        le=10
    )


async def perform_web_search(
    query: str, 
    max_results: int = 5,
    serper_api_key: Optional[str] = None
) -> WebSearchResponse:
    """
    Perform web search using Serper API
    
    Args:
        query: Search query string
        max_results: Maximum number of results to return (1-10)
        serper_api_key: Optional API key override
        
    Returns:
        WebSearchResponse with search results
        
    Raises:
        ValueError: If API key is not provided
        aiohttp.ClientError: If API request fails
    """
    api_key = serper_api_key or os.getenv("SERPER_API_KEY")
    if not api_key:
        raise ValueError("SERPER_API_KEY not found in environment variables")
    
    url = "https://google.serper.dev/search"
    
    headers = {
        "X-API-KEY": api_key,
        "Content-Type": "application/json"
    }
    
    payload = {
        "q": query,
        "num": min(max_results, 10)  # Serper allows max 100, but we limit to 10
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(url, json=payload, headers=headers) as response:
                response.raise_for_status()
                data = await response.json()
                
                # Parse organic results
                results = []
                for idx, item in enumerate(data.get("organic", [])[:max_results]):
                    results.append(WebSearchResult(
                        title=item.get("title", ""),
                        link=item.get("link", ""),
                        snippet=item.get("snippet", ""),
                        position=idx + 1
                    ))
                
                # Create response
                return WebSearchResponse(
                    query=query,
                    results=results,
                    answer_box=data.get("answerBox"),
                    knowledge_graph=data.get("knowledgeGraph")
                )
                
        except aiohttp.ClientResponseError as e:
            if e.status == 401:
                raise ValueError("Invalid SERPER_API_KEY")
            elif e.status == 429:
                raise ValueError("Serper API rate limit exceeded")
            else:
                raise ValueError(f"Serper API error: {e.status} - {e.message}")
        except Exception as e:
            raise ValueError(f"Failed to perform web search: {str(e)}")


def get_web_search_tool_schema():
    """
    Get OpenAI-compatible tool schema for web search
    
    Returns:
        Dict containing the tool schema for OpenAI function calling
    """
    return {
        "type": "function",
        "function": {
            "name": "web_search",
            "description": "Search the web for current information using Google search",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to look up on the web"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 10
                    }
                },
                "required": ["query"]
            }
        }
    }


async def process_tool_call(tool_call) -> Dict[str, Any]:
    """
    Process a web search tool call from OpenAI
    
    Args:
        tool_call: OpenAI tool call object
        
    Returns:
        Dict with tool response in OpenAI format
    """
    try:
        # Parse arguments
        args = json.loads(tool_call.function.arguments)
        
        # Perform search
        search_response = await perform_web_search(
            query=args["query"],
            max_results=args.get("max_results", 5)
        )
        
        # Format response for OpenAI
        response_content = {
            "query": search_response.query,
            "results": [
                {
                    "title": r.title,
                    "url": r.link,
                    "snippet": r.snippet
                }
                for r in search_response.results
            ]
        }
        
        # Add answer box if available
        if search_response.answer_box:
            response_content["answer"] = search_response.answer_box.get("answer", 
                                        search_response.answer_box.get("snippet"))
        
        # Add knowledge graph if available
        if search_response.knowledge_graph:
            kg = search_response.knowledge_graph
            response_content["knowledge"] = {
                "title": kg.get("title"),
                "type": kg.get("type"),
                "description": kg.get("description")
            }
        
        return {
            "role": "tool",
            "content": json.dumps(response_content),
            "tool_call_id": tool_call.id
        }
        
    except Exception as e:
        # Return error response
        return {
            "role": "tool",
            "content": json.dumps({
                "error": f"Search failed: {str(e)}"
            }),
            "tool_call_id": tool_call.id
        }


# Example usage for testing
if __name__ == "__main__":
    async def test_search():
        try:
            response = await perform_web_search("OpenAI GPT-4 latest news", max_results=3)
            print(f"Query: {response.query}")
            print(f"Found {len(response.results)} results:")
            for result in response.results:
                print(f"\n{result.position}. {result.title}")
                print(f"   URL: {result.link}")
                print(f"   Snippet: {result.snippet}")
        except Exception as e:
            print(f"Error: {e}")
    
    asyncio.run(test_search())